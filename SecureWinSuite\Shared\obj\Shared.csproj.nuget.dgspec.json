{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\windsurf loader\\SecureWinSuite\\Shared\\Shared.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\windsurf loader\\SecureWinSuite\\Shared\\Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\windsurf loader\\SecureWinSuite\\Shared\\Shared.csproj", "projectName": "SecureWinSuite.Shared", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\windsurf loader\\SecureWinSuite\\Shared\\Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\windsurf loader\\SecureWinSuite\\Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}