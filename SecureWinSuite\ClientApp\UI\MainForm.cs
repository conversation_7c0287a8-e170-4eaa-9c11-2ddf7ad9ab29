using System;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using SecureWinSuite.ClientApp.Networking;
using SecureWinSuite.ClientApp.Security;
using SecureWinSuite.Shared.Logging;
using SecureWinSuite.Shared.Networking;
using SecureWinSuite.Shared.Security;

namespace SecureWinSuite.ClientApp.UI
{
    public partial class MainForm : Form
    {
        private ClientConnection? _conn;
        private string _hwid = string.Empty;
        private readonly SecurityMonitor _security = new SecurityMonitor();
        private CancellationTokenSource _cts = new CancellationTokenSource();

        public MainForm()
        {
            InitializeComponent();
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            Logger.Init();
            try { _hwid = HardwareFingerprint.GetHwid(); }
            catch { _hwid = Guid.NewGuid().ToString("N"); }
            txtLog.AppendText($"HWID: {_hwid}{Environment.NewLine}");

            // Pre-login silent detection
            if (_security.CheckPreLogin(out var report))
            {
                // Attempt to report silently, then exit
                _ = Task.Run(async () =>
                {
                    try
                    {
                        using var c = new ClientConnection();
                        await c.ConnectAsync(_cts.Token);
                        report.Hwid = _hwid;
                        await c.ReportViolationAsync(report);
                    }
                    catch { }
                    finally { Environment.Exit(0); }
                });
            }
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            var serial = txtSerial.Text.Trim();
            var product = txtProduct.Text.Trim();
            if (string.IsNullOrEmpty(serial) || string.IsNullOrEmpty(product))
            {
                MessageBox.Show("Enter serial key and product ID.", "Missing info", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            btnLogin.Enabled = false;
            try
            {
                _conn = new ClientConnection();
                _conn.LoginReceived += OnLoginReceived;
                _conn.Disconnected += OnDisconnected;
                await _conn.ConnectAsync(_cts.Token);

                var req = new LoginRequest
                {
                    SerialKey = serial,
                    ProductId = product,
                    Hwid = _hwid,
                    ClientVersion = Application.ProductVersion
                };
                await _conn.SendLoginAsync(req, _cts.Token);
                AppendLog("Login request sent.");
            }
            catch (Exception ex)
            {
                AppendLog($"Login failed: {ex.Message}");
                btnLogin.Enabled = true;
            }
        }

        private void OnLoginReceived(object? sender, LoginResponse e)
        {
            if (InvokeRequired)
            {
                BeginInvoke(new Action<object?, LoginResponse>(OnLoginReceived), sender, e);
                return;
            }

            AppendLog($"Login response: {(e.Success ? "Success" : "Failed")} - {e.Message}");

            if (e.Success)
            {
                btnLogout.Enabled = true;
                _conn?.StartHeartbeat();
                _security.StartPostLoginMonitoring(_conn!, txtSerial.Text.Trim(), txtProduct.Text.Trim(), _hwid);
            }
            else
            {
                if (e.Locked)
                {
                    MessageBox.Show("Account is locked.", "Access denied", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                btnLogin.Enabled = true;
            }
        }

        private void OnDisconnected(object? sender, string e)
        {
            if (InvokeRequired)
            {
                BeginInvoke(new Action<object?, string>(OnDisconnected), sender, e);
                return;
            }
            AppendLog($"Disconnected: {e}");
            btnLogin.Enabled = true;
            btnLogout.Enabled = false;
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            try { _conn?.Dispose(); } catch { }
            btnLogin.Enabled = true;
            btnLogout.Enabled = false;
        }

        private void AppendLog(string msg)
        {
            txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] {msg}{Environment.NewLine}");
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            base.OnClosing(e);
            try { _cts.Cancel(); } catch { }
            try { _conn?.Dispose(); } catch { }
        }
    }
}
