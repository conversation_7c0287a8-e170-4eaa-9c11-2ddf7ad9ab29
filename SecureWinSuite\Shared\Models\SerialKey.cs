using System;

namespace SecureWinSuite.Shared.Models
{
    public class SerialKey
    {
        public string ProductId { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public DateTime ExpirationUtc { get; set; }
        public string? BoundHwid { get; set; }
        public bool Locked { get; set; }
        public int AllowedHwidResets { get; set; } = 0;
        public DateTime CreatedUtc { get; set; } = DateTime.UtcNow;
        public DateTime? LastUsedUtc { get; set; }

        public bool IsExpired(DateTime utcNow) => utcNow >= ExpirationUtc;
    }
}
