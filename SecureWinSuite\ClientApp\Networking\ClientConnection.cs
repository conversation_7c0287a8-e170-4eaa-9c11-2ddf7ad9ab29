using System;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using SecureWinSuite.ClientApp.Configuration;
using SecureWinSuite.Shared.Logging;
using SecureWinSuite.Shared.Networking;

namespace SecureWinSuite.ClientApp.Networking
{
    public class ClientConnection : IDisposable
    {
        private TcpClient? _tcp;
        private AesTcpProtocol? _protocol;
        private CancellationTokenSource? _cts;
        private Task? _recvTask;
        private Task? _heartbeatTask;
        private string _clientId = Guid.NewGuid().ToString("N");

        public bool IsConnected => _tcp != null && _tcp.Connected;

        public event EventHandler<LoginResponse>? LoginReceived;
        public event EventHandler<string>? Disconnected;

        public async Task ConnectAsync(CancellationToken ct)
        {
            if (_tcp != null) return;
            _tcp = new TcpClient { NoDelay = true };
            await _tcp.ConnectAsync(ClientConfig.Host, ClientConfig.Port).ConfigureAwait(false);
            _protocol = new AesTcpProtocol(ClientConfig.Passphrase, ClientConfig.Salt, ClientConfig.Iterations);
            _cts = CancellationTokenSource.CreateLinkedTokenSource(ct);
            var stream = _tcp.GetStream();
            _recvTask = Task.Run(async () => await ReceiveLoopAsync(stream, _cts.Token));
        }

        public async Task SendLoginAsync(LoginRequest req, CancellationToken ct)
        {
            if (_protocol == null || _tcp == null) throw new InvalidOperationException("Not connected");
            await _protocol.SendAsync(_tcp.GetStream(), PacketType.LoginRequest, req, ct).ConfigureAwait(false);
        }

        public void StartHeartbeat()
        {
            if (_tcp == null || _protocol == null || _heartbeatTask != null) return;
            var stream = _tcp.GetStream();
            _heartbeatTask = Task.Run(async () =>
            {
                while (_cts != null && !_cts.IsCancellationRequested)
                {
                    try
                    {
                        var hb = new Heartbeat { ClientId = _clientId };
                        await _protocol.SendAsync(stream, PacketType.Heartbeat, hb, _cts.Token).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        Logger.Warn($"Heartbeat failed: {ex.Message}");
                        break;
                    }
                    await Task.Delay(ClientConfig.HeartbeatInterval, _cts.Token).ConfigureAwait(false);
                }
            });
        }

        public async Task ReportViolationAsync(ViolationReport report)
        {
            if (_protocol == null || _tcp == null) return;
            var type = report.PostLogin ? PacketType.ViolationPostLogin : PacketType.ViolationPreLogin;
            try
            {
                await _protocol.SendAsync(_tcp.GetStream(), type, report, _cts?.Token ?? CancellationToken.None).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                Logger.Warn($"ReportViolation failed: {ex.Message}");
            }
        }

        private async Task ReceiveLoopAsync(NetworkStream stream, CancellationToken ct)
        {
            if (_protocol == null) return;
            try
            {
                while (!ct.IsCancellationRequested)
                {
                    Packet? packet = null;
                    try
                    {
                        packet = await _protocol.ReceiveAsync(stream, ct).ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        Disconnected?.Invoke(this, ex.Message);
                        break;
                    }
                    if (packet == null) continue;
                    if (packet.Type == PacketType.LoginResponse)
                    {
                        var lr = packet.GetPayload<LoginResponse>();
                        if (lr != null)
                        {
                            LoginReceived?.Invoke(this, lr);
                        }
                    }
                }
            }
            finally
            {
                Dispose();
            }
        }

        public void Dispose()
        {
            try { _cts?.Cancel(); } catch { }
            try { _tcp?.Close(); } catch { }
            _heartbeatTask = null;
            _recvTask = null;
            _protocol = null;
            _tcp = null;
            _cts = null;
        }
    }
}
