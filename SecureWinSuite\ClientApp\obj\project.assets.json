{"version": 3, "targets": {".NETFramework,Version=v4.7.2": {"Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "SecureWinSuite.Shared/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.7.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/SecureWinSuite.Shared.dll": {}}, "runtime": {"bin/placeholder/SecureWinSuite.Shared.dll": {}}}}, ".NETFramework,Version=v4.7.2/win-x86": {"Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "SecureWinSuite.Shared/1.0.0": {"type": "project", "framework": ".NETFramework,Version=v4.7.2", "dependencies": {"Newtonsoft.Json": "13.0.3"}, "compile": {"bin/placeholder/SecureWinSuite.Shared.dll": {}}, "runtime": {"bin/placeholder/SecureWinSuite.Shared.dll": {}}}}}, "libraries": {"Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "SecureWinSuite.Shared/1.0.0": {"type": "project", "path": "../Shared/Shared.csproj", "msbuildProject": "../Shared/Shared.csproj"}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.7.2": ["Newtonsoft.Json >= 13.0.3", "SecureWinSuite.Shared >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\windsurf loader\\SecureWinSuite\\ClientApp\\ClientApp.csproj", "projectName": "SecureWinSuite.ClientApp", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\windsurf loader\\SecureWinSuite\\ClientApp\\ClientApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\windsurf loader\\SecureWinSuite\\ClientApp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\windsurf loader\\SecureWinSuite\\Shared\\Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Masaüstü\\coding\\horizon beta\\loader c#\\windsurf loader\\SecureWinSuite\\Shared\\Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}