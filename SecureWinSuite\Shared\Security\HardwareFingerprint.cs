using System;
using System.Linq;
using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace SecureWinSuite.Shared.Security
{
    public static class HardwareFingerprint
    {
        public static string GetHwid()
        {
            var sb = new StringBuilder();
            try { sb.AppendLine($"CPU:{Query<PERSON>mi("Win32_Processor", "ProcessorId")}"); } catch {}
            try { sb.AppendLine($"BaseBoard:{QueryWmi("Win32_BaseBoard", "SerialNumber")}"); } catch {}
            try { sb.AppendLine($"BIOS:{QueryWmi("Win32_BIOS", "SerialNumber")}"); } catch {}
            try { sb.AppendLine($"Disk:{QueryWmi("Win32_DiskDrive", "SerialNumber")}"); } catch {}
            try { sb.AppendLine($"UUID:{QueryWmi("Win32_ComputerSystemProduct", "UUID")}"); } catch {}
            try { sb.AppendLine($"MACs:{string.Join(",", GetMacs())}"); } catch {}

            using var sha = SHA256.Create();
            var bytes = Encoding.UTF8.GetBytes(sb.ToString());
            var hash = sha.ComputeHash(bytes);
            return BitConverter.ToString(hash).Replace("-", string.Empty);
        }

        private static string QueryWmi(string path, string property)
        {
            using var searcher = new ManagementObjectSearcher($"select {property} from {path}");
            foreach (var o in searcher.Get())
            {
                var mo = (ManagementObject)o;
                var val = mo[property]?.ToString();
                if (!string.IsNullOrWhiteSpace(val)) return val.Trim();
            }
            return string.Empty;
        }

        private static string[] GetMacs()
        {
            using var searcher = new ManagementObjectSearcher("SELECT MACAddress FROM Win32_NetworkAdapter WHERE MACAddress IS NOT NULL");
            return searcher.Get().Cast<ManagementObject>()
                .Select(mo => mo["MACAddress"]?.ToString() ?? string.Empty)
                .Where(s => !string.IsNullOrWhiteSpace(s))
                .Distinct()
                .OrderBy(s => s)
                .ToArray();
        }
    }
}
