using System;
using System.ComponentModel;
using System.Windows.Forms;
using SecureWinSuite.ServerApp.Networking;
using SecureWinSuite.ServerApp.UI;

namespace SecureWinSuite.ServerApp.UI
{
    public partial class MainForm : Form
    {
        private TcpServer? _server;

        public MainForm()
        {
            InitializeComponent();
        }

        private void btnStart_Click(object sender, EventArgs e)
        {
            if (_server != null) return;
            _server = new TcpServer();
            _server.StatusChanged += OnStatusChanged;
            _server.Start();
            btnStart.Enabled = false;
            btnStop.Enabled = true;
        }

        private void btnStop_Click(object sender, EventArgs e)
        {
            _server?.Stop();
            _server = null;
            btnStart.Enabled = true;
            btnStop.Enabled = false;
        }

        private void OnStatusChanged(object? sender, string e)
        {
            if (InvokeRequired)
            {
                BeginInvoke(new Action<object?, string>(OnStatusChanged), sender, e);
                return;
            }
            txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] {e}{Environment.NewLine}");
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            base.OnClosing(e);
            _server?.Stop();
        }

        private void btnAdmin_Click(object sender, EventArgs e)
        {
            using (var f = new AdminPanelForm())
            {
                f.StartPosition = FormStartPosition.CenterParent;
                f.ShowDialog(this);
            }
        }
    }
}
