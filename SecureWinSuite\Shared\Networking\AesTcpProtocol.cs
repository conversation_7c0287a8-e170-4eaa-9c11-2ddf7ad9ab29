using System;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using SecureWinSuite.Shared.Crypto;
using Newtonsoft.Json;

namespace SecureWinSuite.Shared.Networking
{
    public class AesTcpProtocol
    {
        private readonly string _passphrase;
        private readonly byte[] _salt;
        private readonly int _iterations;

        public AesTcpProtocol(string passphrase, byte[] salt, int iterations)
        {
            _passphrase = passphrase ?? throw new ArgumentNullException(nameof(passphrase));
            _salt = salt ?? throw new ArgumentNullException(nameof(salt));
            _iterations = iterations;
        }

        public async Task SendAsync<T>(NetworkStream stream, PacketType type, T payload, CancellationToken ct)
        {
            var packet = Packet.Create(type, payload);
            var json = JsonConvert.SerializeObject(packet);
            var plaintext = Encoding.UTF8.GetBytes(json);
            var ciphertext = AesCrypto.Encrypt(plaintext, _passphrase, _salt, _iterations);

            var lengthBytes = BitConverter.GetBytes(ciphertext.Length);
            if (!BitConverter.IsLittleEndian) Array.Reverse(lengthBytes);
            await stream.WriteAsync(lengthBytes, 0, lengthBytes.Length, ct).ConfigureAwait(false);
            await stream.WriteAsync(ciphertext, 0, ciphertext.Length, ct).ConfigureAwait(false);
            await stream.FlushAsync(ct).ConfigureAwait(false);
        }

        public async Task<Packet?> ReceiveAsync(NetworkStream stream, CancellationToken ct)
        {
            var lengthBytes = await ReadExactAsync(stream, 4, ct).ConfigureAwait(false);
            if (!BitConverter.IsLittleEndian) Array.Reverse(lengthBytes);
            var length = BitConverter.ToInt32(lengthBytes, 0);
            if (length <= 0 || length > 10_000_000) throw new IOException("Invalid frame length");

            var data = await ReadExactAsync(stream, length, ct).ConfigureAwait(false);
            var json = AesCrypto.DecryptToString(data, _passphrase, _salt, _iterations);
            return JsonConvert.DeserializeObject<Packet>(json);
        }

        private static async Task<byte[]> ReadExactAsync(NetworkStream stream, int count, CancellationToken ct)
        {
            var buffer = new byte[count];
            int read = 0;
            while (read < count)
            {
                var n = await stream.ReadAsync(buffer, read, count - read, ct).ConfigureAwait(false);
                if (n == 0) throw new IOException("Connection closed");
                read += n;
            }
            return buffer;
        }
    }
}
