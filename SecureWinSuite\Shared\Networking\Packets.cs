using System;
using Newtonsoft.Json;

namespace SecureWinSuite.Shared.Networking
{
    public enum PacketType
    {
        Heartbeat,
        LoginRequest,
        LoginResponse,
        ViolationPreLogin,
        ViolationPostLogin
    }

    public class Packet
    {
        public PacketType Type { get; set; }
        public string PayloadJson { get; set; } = string.Empty;

        public static Packet Create<T>(PacketType type, T payload)
        {
            return new Packet
            {
                Type = type,
                PayloadJson = JsonConvert.SerializeObject(payload)
            };
        }

        public T? GetPayload<T>()
        {
            return JsonConvert.DeserializeObject<T>(PayloadJson);
        }
    }

    public class Heartbeat
    {
        public DateTime Utc { get; set; } = DateTime.UtcNow;
        public string ClientId { get; set; } = string.Empty;
    }

    public class LoginRequest
    {
        public string SerialKey { get; set; } = string.Empty;
        public string ProductId { get; set; } = string.Empty;
        public string Hwid { get; set; } = string.Empty;
        public string ClientVersion { get; set; } = "1.0.0";
    }

    public class LoginResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime? ExpirationUtc { get; set; }
        public bool Locked { get; set; }
    }

    public class ViolationReport
    {
        public DateTime TimestampUtc { get; set; } = DateTime.UtcNow;
        public string ViolationType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string SerialKey { get; set; } = string.Empty;
        public string ProductId { get; set; } = string.Empty;
        public string Hwid { get; set; } = string.Empty;
        public bool PostLogin { get; set; }
        public string? ContextJson { get; set; }
    }
}
