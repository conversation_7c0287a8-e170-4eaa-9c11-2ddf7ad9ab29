using System;
using System.Configuration;
using System.Text;

namespace SecureWinSuite.ServerApp.Configuration
{
    public static class ServerConfig
    {
        public static int Port => int.TryParse(ConfigurationManager.AppSettings["ServerPort"], out var p) ? p : 5055;
        public static string Passphrase => ConfigurationManager.AppSettings["CryptoPassphrase"] ?? "ChangeThisPassphrase";
        public static byte[] Salt => Convert.FromBase64String(ConfigurationManager.AppSettings["CryptoSaltBase64"] ?? "bXlTdXBlclNhbHQxMjM0NQ==");
        public static int Iterations => int.TryParse(ConfigurationManager.AppSettings["CryptoIterations"], out var i) ? i : 100_000;
        public static bool LockOnMultipleHwid => bool.TryParse(ConfigurationManager.AppSettings["LockOnMultipleHWID"], out var b) && b;
        public static string DataDirectory => ConfigurationManager.AppSettings["DataDirectory"] ?? "Data";
    }
}
