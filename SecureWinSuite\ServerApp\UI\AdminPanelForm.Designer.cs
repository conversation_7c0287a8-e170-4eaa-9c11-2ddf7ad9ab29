namespace SecureWinSuite.ServerApp.UI
{
    partial class AdminPanelForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        private System.Windows.Forms.TabControl tabControl;
        private System.Windows.Forms.TabPage tabKeys;
        private System.Windows.Forms.TabPage tabIncidents;
        private System.Windows.Forms.TabPage tabBlacklist;

        private System.Windows.Forms.DataGridView dgvKeys;
        private System.Windows.Forms.DataGridView dgvIncidents;
        private System.Windows.Forms.DataGridView dgvBlacklist;

        private System.Windows.Forms.GroupBox grpGenerate;
        private System.Windows.Forms.TextBox txtProductId;
        private System.Windows.Forms.TextBox txtProductName;
        private System.Windows.Forms.DateTimePicker dtpExpiration;
        private System.Windows.Forms.NumericUpDown numHwidResets;
        private System.Windows.Forms.Button btnGenerate;
        private System.Windows.Forms.Button btnRefresh;

        private System.Windows.Forms.GroupBox grpOps;
        private System.Windows.Forms.TextBox txtSerialForOps;
        private System.Windows.Forms.TextBox txtProductForOps;
        private System.Windows.Forms.Button btnResetHwid;
        private System.Windows.Forms.Button btnUnlock;

        private System.Windows.Forms.TextBox txtHwid;
        private System.Windows.Forms.Button btnRemoveBlacklist;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.tabControl = new System.Windows.Forms.TabControl();
            this.tabKeys = new System.Windows.Forms.TabPage();
            this.tabIncidents = new System.Windows.Forms.TabPage();
            this.tabBlacklist = new System.Windows.Forms.TabPage();
            this.dgvKeys = new System.Windows.Forms.DataGridView();
            this.dgvIncidents = new System.Windows.Forms.DataGridView();
            this.dgvBlacklist = new System.Windows.Forms.DataGridView();
            this.grpGenerate = new System.Windows.Forms.GroupBox();
            this.txtProductId = new System.Windows.Forms.TextBox();
            this.txtProductName = new System.Windows.Forms.TextBox();
            this.dtpExpiration = new System.Windows.Forms.DateTimePicker();
            this.numHwidResets = new System.Windows.Forms.NumericUpDown();
            this.btnGenerate = new System.Windows.Forms.Button();
            this.btnRefresh = new System.Windows.Forms.Button();
            this.grpOps = new System.Windows.Forms.GroupBox();
            this.txtSerialForOps = new System.Windows.Forms.TextBox();
            this.txtProductForOps = new System.Windows.Forms.TextBox();
            this.btnResetHwid = new System.Windows.Forms.Button();
            this.btnUnlock = new System.Windows.Forms.Button();
            this.txtHwid = new System.Windows.Forms.TextBox();
            this.btnRemoveBlacklist = new System.Windows.Forms.Button();
            this.tabControl.SuspendLayout();
            this.tabKeys.SuspendLayout();
            this.tabIncidents.SuspendLayout();
            this.tabBlacklist.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvKeys)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvIncidents)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvBlacklist)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHwidResets)).BeginInit();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl.Controls.Add(this.tabKeys);
            this.tabControl.Controls.Add(this.tabIncidents);
            this.tabControl.Controls.Add(this.tabBlacklist);
            this.tabControl.Location = new System.Drawing.Point(12, 12);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new System.Drawing.Size(860, 537);
            this.tabControl.TabIndex = 0;
            // 
            // tabKeys
            // 
            this.tabKeys.Controls.Add(this.dgvKeys);
            this.tabKeys.Controls.Add(this.grpGenerate);
            this.tabKeys.Controls.Add(this.grpOps);
            this.tabKeys.Controls.Add(this.btnRefresh);
            this.tabKeys.Location = new System.Drawing.Point(4, 29);
            this.tabKeys.Name = "tabKeys";
            this.tabKeys.Padding = new System.Windows.Forms.Padding(3);
            this.tabKeys.Size = new System.Drawing.Size(852, 504);
            this.tabKeys.TabIndex = 0;
            this.tabKeys.Text = "Keys";
            this.tabKeys.UseVisualStyleBackColor = true;
            // 
            // tabIncidents
            // 
            this.tabIncidents.Controls.Add(this.dgvIncidents);
            this.tabIncidents.Location = new System.Drawing.Point(4, 29);
            this.tabIncidents.Name = "tabIncidents";
            this.tabIncidents.Padding = new System.Windows.Forms.Padding(3);
            this.tabIncidents.Size = new System.Drawing.Size(852, 504);
            this.tabIncidents.TabIndex = 1;
            this.tabIncidents.Text = "Incidents";
            this.tabIncidents.UseVisualStyleBackColor = true;
            // 
            // tabBlacklist
            // 
            this.tabBlacklist.Controls.Add(this.dgvBlacklist);
            this.tabBlacklist.Controls.Add(this.txtHwid);
            this.tabBlacklist.Controls.Add(this.btnRemoveBlacklist);
            this.tabBlacklist.Location = new System.Drawing.Point(4, 29);
            this.tabBlacklist.Name = "tabBlacklist";
            this.tabBlacklist.Padding = new System.Windows.Forms.Padding(3);
            this.tabBlacklist.Size = new System.Drawing.Size(852, 504);
            this.tabBlacklist.TabIndex = 2;
            this.tabBlacklist.Text = "Blacklist";
            this.tabBlacklist.UseVisualStyleBackColor = true;
            // 
            // dgvKeys
            // 
            this.dgvKeys.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgvKeys.Location = new System.Drawing.Point(6, 6);
            this.dgvKeys.Name = "dgvKeys";
            this.dgvKeys.ReadOnly = true;
            this.dgvKeys.RowHeadersVisible = false;
            this.dgvKeys.Size = new System.Drawing.Size(840, 270);
            this.dgvKeys.TabIndex = 0;
            this.dgvKeys.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            // 
            // dgvIncidents
            // 
            this.dgvIncidents.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgvIncidents.Location = new System.Drawing.Point(6, 6);
            this.dgvIncidents.Name = "dgvIncidents";
            this.dgvIncidents.ReadOnly = true;
            this.dgvIncidents.RowHeadersVisible = false;
            this.dgvIncidents.Size = new System.Drawing.Size(840, 492);
            this.dgvIncidents.TabIndex = 0;
            this.dgvIncidents.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            // 
            // dgvBlacklist
            // 
            this.dgvBlacklist.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgvBlacklist.Location = new System.Drawing.Point(6, 6);
            this.dgvBlacklist.Name = "dgvBlacklist";
            this.dgvBlacklist.ReadOnly = true;
            this.dgvBlacklist.RowHeadersVisible = false;
            this.dgvBlacklist.Size = new System.Drawing.Size(840, 430);
            this.dgvBlacklist.TabIndex = 0;
            this.dgvBlacklist.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            // 
            // grpGenerate
            // 
            this.grpGenerate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grpGenerate.Location = new System.Drawing.Point(6, 282);
            this.grpGenerate.Name = "grpGenerate";
            this.grpGenerate.Size = new System.Drawing.Size(840, 108);
            this.grpGenerate.TabIndex = 1;
            this.grpGenerate.TabStop = false;
            this.grpGenerate.Text = "Generate Serial Key";
            // Add controls to grpGenerate
            var lblProdId = new System.Windows.Forms.Label();
            lblProdId.Text = "Product ID:";
            lblProdId.Location = new System.Drawing.Point(12, 26);
            lblProdId.AutoSize = true;
            this.grpGenerate.Controls.Add(lblProdId);

            this.txtProductId.Location = new System.Drawing.Point(100, 22);
            this.txtProductId.Width = 150;
            this.grpGenerate.Controls.Add(this.txtProductId);

            var lblProdName = new System.Windows.Forms.Label();
            lblProdName.Text = "Name:";
            lblProdName.Location = new System.Drawing.Point(260, 26);
            lblProdName.AutoSize = true;
            this.grpGenerate.Controls.Add(lblProdName);

            this.txtProductName.Location = new System.Drawing.Point(315, 22);
            this.txtProductName.Width = 150;
            this.grpGenerate.Controls.Add(this.txtProductName);

            var lblExp = new System.Windows.Forms.Label();
            lblExp.Text = "Expires:";
            lblExp.Location = new System.Drawing.Point(475, 26);
            lblExp.AutoSize = true;
            this.grpGenerate.Controls.Add(lblExp);

            this.dtpExpiration.Location = new System.Drawing.Point(540, 22);
            this.dtpExpiration.Width = 180;
            this.grpGenerate.Controls.Add(this.dtpExpiration);

            var lblResets = new System.Windows.Forms.Label();
            lblResets.Text = "HWID Resets:";
            lblResets.Location = new System.Drawing.Point(12, 66);
            lblResets.AutoSize = true;
            this.grpGenerate.Controls.Add(lblResets);

            this.numHwidResets.Location = new System.Drawing.Point(100, 62);
            this.numHwidResets.Minimum = 0;
            this.numHwidResets.Maximum = 100;
            this.numHwidResets.Value = 1;
            this.grpGenerate.Controls.Add(this.numHwidResets);

            this.btnGenerate.Text = "Generate";
            this.btnGenerate.Location = new System.Drawing.Point(315, 60);
            this.btnGenerate.Click += new System.EventHandler(this.btnGenerate_Click);
            this.grpGenerate.Controls.Add(this.btnGenerate);
            // 
            // grpOps
            // 
            this.grpOps.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grpOps.Location = new System.Drawing.Point(6, 396);
            this.grpOps.Name = "grpOps";
            this.grpOps.Size = new System.Drawing.Size(840, 66);
            this.grpOps.TabIndex = 2;
            this.grpOps.TabStop = false;
            this.grpOps.Text = "Operations";
            // Add controls to grpOps
            var lblSerial = new System.Windows.Forms.Label();
            lblSerial.Text = "Serial:";
            lblSerial.Location = new System.Drawing.Point(12, 29);
            lblSerial.AutoSize = true;
            this.grpOps.Controls.Add(lblSerial);

            this.txtSerialForOps.Location = new System.Drawing.Point(60, 26);
            this.txtSerialForOps.Width = 160;
            this.grpOps.Controls.Add(this.txtSerialForOps);

            var lblProd = new System.Windows.Forms.Label();
            lblProd.Text = "Product:";
            lblProd.Location = new System.Drawing.Point(230, 29);
            lblProd.AutoSize = true;
            this.grpOps.Controls.Add(lblProd);

            this.txtProductForOps.Location = new System.Drawing.Point(290, 26);
            this.txtProductForOps.Width = 120;
            this.grpOps.Controls.Add(this.txtProductForOps);

            this.btnResetHwid.Text = "Reset HWID";
            this.btnResetHwid.Location = new System.Drawing.Point(430, 24);
            this.btnResetHwid.Click += new System.EventHandler(this.btnResetHwid_Click);
            this.grpOps.Controls.Add(this.btnResetHwid);

            this.btnUnlock.Text = "Unlock";
            this.btnUnlock.Location = new System.Drawing.Point(530, 24);
            this.btnUnlock.Click += new System.EventHandler(this.btnUnlock_Click);
            this.grpOps.Controls.Add(this.btnUnlock);
            // 
            // btnRefresh
            // 
            this.btnRefresh.Text = "Refresh";
            this.btnRefresh.Location = new System.Drawing.Point(6, 468);
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            this.btnRefresh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.tabKeys.Controls.Add(this.btnRefresh);
            // 
            // tabBlacklist specific controls
            // 
            var lblHwid = new System.Windows.Forms.Label();
            lblHwid.Text = "HWID:";
            lblHwid.Location = new System.Drawing.Point(6, 446);
            lblHwid.AutoSize = true;
            this.tabBlacklist.Controls.Add(lblHwid);

            this.txtHwid.Location = new System.Drawing.Point(56, 442);
            this.txtHwid.Width = 300;
            this.tabBlacklist.Controls.Add(this.txtHwid);

            this.btnRemoveBlacklist.Text = "Remove";
            this.btnRemoveBlacklist.Location = new System.Drawing.Point(362, 440);
            this.btnRemoveBlacklist.Click += new System.EventHandler(this.btnRemoveBlacklist_Click);
            this.tabBlacklist.Controls.Add(this.btnRemoveBlacklist);
            // 
            // AdminPanelForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 20F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(884, 561);
            this.Controls.Add(this.tabControl);
            this.Name = "AdminPanelForm";
            this.Text = "Admin Panel";
            this.tabControl.ResumeLayout(false);
            this.tabKeys.ResumeLayout(false);
            this.tabIncidents.ResumeLayout(false);
            this.tabBlacklist.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvKeys)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvIncidents)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgvBlacklist)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numHwidResets)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion
    }
}
