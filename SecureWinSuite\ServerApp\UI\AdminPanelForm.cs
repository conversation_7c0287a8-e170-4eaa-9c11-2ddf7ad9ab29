using System;
using System.ComponentModel;
using System.Collections.Generic;
using System.Windows.Forms;
using SecureWinSuite.ServerApp.Services;
using SecureWinSuite.Shared.Models;

namespace SecureWinSuite.ServerApp.UI
{
    public partial class AdminPanelForm : Form
    {
        private readonly LicenseService _service = new LicenseService();

        public AdminPanelForm()
        {
            InitializeComponent();
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            LoadData();
        }

        private void LoadData()
        {
            var store = _service.LoadStore();
            dgvKeys.DataSource = new BindingList<SerialKey>(store.SerialKeys);
            dgvIncidents.DataSource = new BindingList<Incident>(store.Incidents);
            dgvBlacklist.DataSource = new BindingList<string>(new List<string>(store.BlacklistedHwids));
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void btnGenerate_Click(object sender, EventArgs e)
        {
            var productId = txtProductId.Text.Trim();
            var productName = txtProductName.Text.Trim();
            var expiration = dtpExpiration.Value.ToUniversalTime();
            var resets = (int)numHwidResets.Value;
            if (string.IsNullOrWhiteSpace(productId))
            {
                MessageBox.Show("Product ID is required.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            var key = _service.CreateSerialKey(productId, productName, expiration, resets);
            MessageBox.Show($"Key created: {key.Key}", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            LoadData();
        }

        private void btnResetHwid_Click(object sender, EventArgs e)
        {
            var serial = txtSerialForOps.Text.Trim();
            var product = txtProductForOps.Text.Trim();
            if (string.IsNullOrWhiteSpace(serial) || string.IsNullOrWhiteSpace(product))
            {
                MessageBox.Show("Enter Serial and Product.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            if (_service.ResetHwid(serial, product, out var msg))
                MessageBox.Show(msg, "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            else
                MessageBox.Show(msg, "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
            LoadData();
        }

        private void btnUnlock_Click(object sender, EventArgs e)
        {
            var serial = txtSerialForOps.Text.Trim();
            var product = txtProductForOps.Text.Trim();
            if (string.IsNullOrWhiteSpace(serial) || string.IsNullOrWhiteSpace(product))
            {
                MessageBox.Show("Enter Serial and Product.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            var ok = _service.UnlockAccount(serial, product);
            MessageBox.Show(ok ? "Unlocked" : "No change", ok ? "Success" : "Info", MessageBoxButtons.OK,
                ok ? MessageBoxIcon.Information : MessageBoxIcon.Information);
            LoadData();
        }

        private void btnRemoveBlacklist_Click(object sender, EventArgs e)
        {
            var hwid = txtHwid.Text.Trim();
            if (string.IsNullOrWhiteSpace(hwid))
            {
                MessageBox.Show("Enter HWID.", "Validation", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            var removed = _service.RemoveFromBlacklist(hwid);
            MessageBox.Show(removed ? "Removed" : "Not found", removed ? "Success" : "Info", MessageBoxButtons.OK,
                MessageBoxIcon.Information);
            LoadData();
        }
    }
}
